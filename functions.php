<?php

// <PERSON>ı Mega Menü için
class Dmr_Walker_Nav_Menu extends Walker_Nav_Menu {
    function start_lvl( &$output, $depth = 0, $args = array() ) {
        // 'has-mega-menu' sınıfına sahip menüler için özel container
        if ( in_array( 'has-mega-menu', $args->walker->parent_item->classes ) ) {
            $output .= '<ul class="sub-menu"><div class="sub-menu-container">';
        } else {
            $output .= '<ul class="sub-menu">';
        }
    }

    function end_lvl( &$output, $depth = 0, $args = array() ) {
        if ( in_array( 'has-mega-menu', $args->walker->parent_item->classes ) ) {
            $output .= '</div></ul>';
        } else {
            $output .= '</ul>';
        }
    }
    
    // start_el fonksiyonu, parent_item'ı bir sonraki seviyeye (start_lvl) aktarmak için kullanılır.
    function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
        $args->walker->parent_item = $item;
        parent::start_el( $output, $item, $depth, $args, $id );
    }
}


function dmrthema_setup() {
    // Otomatik feed linkleri ekler
    add_theme_support( 'automatic-feed-links' );

    // Başlık etiketini WordPress'in yönetmesine izin verir
    add_theme_support( 'title-tag' );

    // Öne çıkan görselleri etkinleştirir
    add_theme_support( 'post-thumbnails' );

    // Menüleri kaydeder
    register_nav_menus( array(
        'primary' => esc_html__( 'Primary Menu', 'dmrthema' ),
    ) );

    // HTML5 desteği
    add_theme_support( 'html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ) );

    // WooCommerce desteği
    add_theme_support( 'woocommerce' );

    // WooCommerce product gallery desteği
    add_theme_support( 'wc-product-gallery-zoom' );
    add_theme_support( 'wc-product-gallery-lightbox' );
    add_theme_support( 'wc-product-gallery-slider' );
}
add_action( 'after_setup_theme', 'dmrthema_setup' );

// Stil dosyalarını ve scriptleri ekler
function dmrthema_scripts() {
    // Ana stil dosyası
    wp_enqueue_style( 'dmrthema-style', get_stylesheet_uri() );

    // Sadece ana sayfada slider scriptlerini yükle
    if ( is_front_page() ) {
        // Swiper CSS
        wp_enqueue_style( 'swiper-css', 'https://unpkg.com/swiper/swiper-bundle.min.css' );
        
        // Swiper JS
        wp_enqueue_script( 'swiper-js', 'https://unpkg.com/swiper/swiper-bundle.min.js', array(), null, true );
        
        // Slider başlatma scripti
        wp_enqueue_script( 'slider-init', get_template_directory_uri() . '/js/slider-init.js', array('swiper-js'), null, true );
    }

    // WooCommerce stilleri ve scriptleri
    if ( class_exists( 'WooCommerce' ) ) {
        if ( is_woocommerce() || is_cart() || is_checkout() || is_account_page() ) {
            wp_enqueue_style( 'dmrthema-woocommerce', get_template_directory_uri() . '/assets/css/woocommerce.css', array(), '1.0.0' );
        }

        if ( is_product() ) {
            wp_enqueue_style( 'dmrthema-product', get_template_directory_uri() . '/assets/css/product.css', array(), '1.0.0' );
            wp_enqueue_script( 'dmrthema-product-js', get_template_directory_uri() . '/assets/js/product.js', array('jquery'), '1.0.0', true );
            wp_enqueue_script( 'dmrthema-quantity', get_template_directory_uri() . '/assets/js/quantity.min.js', array('jquery'), '1.0.0', true );
        }
    }
}
add_action( 'wp_enqueue_scripts', 'dmrthema_scripts' );

// Slider Custom Post Type'ı kaydet
function dmrthema_register_slider_cpt() {
    $args = array(
        'public'       => true,
        'label'        => 'Slider',
        'labels'       => array(
            'name'          => 'Slider',
            'singular_name' => 'Slider Öğesi',
            'add_new_item'  => 'Yeni Slider Öğesi Ekle',
            'edit_item'     => 'Slider Öğesini Düzenle',
            'new_item'      => 'Yeni Slider Öğesi',
            'view_item'     => 'Slider Öğesini Görüntüle',
            'search_items'  => 'Slider Öğesi Ara',
            'not_found'     => 'Slider öğesi bulunamadı',
        ),
        'supports'     => array('title', 'thumbnail', 'editor'),
        'show_in_menu' => 'dmrthema_settings', // DmrThema menüsünün altına ekle
        'menu_icon'    => 'dashicons-images-alt2',
    );
    register_post_type('slider', $args);
}
add_action('init', 'dmrthema_register_slider_cpt');

// Tema Ayarları Menüsünü oluştur
function dmrthema_admin_menu() {
    add_menu_page(
        'DmrThema Ayarları',
        'DmrThema',
        'manage_options',
        'dmrthema_settings',
        'dmrthema_settings_page_html',
        'dashicons-admin-customizer',
        20
    );
}
add_action('admin_menu', 'dmrthema_admin_menu');

// Ana ayar sayfası için boş bir fonksiyon (şimdilik)
function dmrthema_settings_page_html() {
    echo '<h1>DmrThema Ayarları</h1>';
}

/**
 * WooCommerce Entegrasyonu
 */
if ( class_exists( 'WooCommerce' ) ) {
    // WooCommerce template functions dosyasını yükle
    require_once get_template_directory() . '/inc/woocommerce/dmrthema-woocommerce-template-functions.php';

    // WooCommerce template hooks dosyasını yükle
    require_once get_template_directory() . '/inc/woocommerce/dmrthema-woocommerce-template-hooks.php';
}

/**
 * WooCommerce için özel fonksiyonlar
 */

// WooCommerce ürün sayısını ayarla
function dmrthema_woocommerce_product_columns() {
    return 3; // 3 sütun
}
add_filter( 'loop_shop_columns', 'dmrthema_woocommerce_product_columns' );

// WooCommerce sayfa başına ürün sayısını ayarla
function dmrthema_woocommerce_products_per_page() {
    return 12; // Sayfa başına 12 ürün
}
add_filter( 'loop_shop_per_page', 'dmrthema_woocommerce_products_per_page', 20 );

// WooCommerce sidebar'ı kaldır
function dmrthema_remove_woocommerce_sidebar() {
    if ( is_shop() || is_product_category() || is_product_tag() || is_product() ) {
        remove_action( 'woocommerce_sidebar', 'woocommerce_get_sidebar', 10 );
    }
}
add_action( 'wp', 'dmrthema_remove_woocommerce_sidebar' );

// WooCommerce için widget alanları
function dmrthema_woocommerce_widgets_init() {
    register_sidebar( array(
        'name'          => esc_html__( 'Shop Sidebar', 'dmrthema' ),
        'id'            => 'shop-sidebar',
        'description'   => esc_html__( 'Add widgets here to appear in shop pages.', 'dmrthema' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );

    register_sidebar( array(
        'name'          => esc_html__( 'Single Product Field', 'dmrthema' ),
        'id'            => 'single-product-field',
        'description'   => esc_html__( 'Add widgets here to appear on single product pages.', 'dmrthema' ),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h2 class="widget-title">',
        'after_title'   => '</h2>',
    ) );
}
add_action( 'widgets_init', 'dmrthema_woocommerce_widgets_init' );

?>
